<!DOCTYPE HTML>
<html theme-platform="eui">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
  <link rel="preconnect" href="https://statics.ekuaibao.com/" crossorigin />
  <link rel="dns-prefetch" href="https://statics.ekuaibao.com/" />
  <link rel="dns-prefetch" href="https://mfe-server.serverless.ekuaibao.com/" />
  <link rel="dns-prefetch" href="https://images.ekuaibao.com" />
  <script>
    // 各环境配置
    window.ENV_CONFIG = '<ENV_CONFIG_PLACEHOLDER>'
    if (typeof window.ENV_CONFIG === 'string' && !window.ENV_CONFIG.includes('ENV_CONFIG_PLACEHOLDER')) {
      try {
        window.ENV_CONFIG = JSON.parse(window.ENV_CONFIG)
      } catch (error) {
        window.ENV_CONFIG = {}
      }
    }
    window.getEnvConfigValue = function (key, defaultValue) {
      return window.ENV_CONFIG[key] || defaultValue
    }
  </script>
  <script>
    var CWRUMLICENCE = 'J45Engw88NeyneJHDCI8SBeULL**KLb**3';
  </script>
  <script src="https://statics.ekuaibao.com/hose/hose-monitor/monitor-3.1.1.js"></script>
  <script>
    !function () {
      const dkOrigin = window.getEnvConfigValue('ddTraceURL', 'https://hotfix.ekuaibao.net/dk/');
      window.HSM = HoseMonitor.init({
        applicationId: 'appid_web_eECpzrHci5rDPY29C2GM',
        datakitOrigin: dkOrigin,
        version: '{{APPLICATION_VERSION}}',
        allowedTracingOrigins:[window.location.origin],
        beforeSend: function(event) {
          if (event.type === 'resource') {
            return event.resource.url_host === location.host
          }
          return true
        }
      })
      window.HSM && window.HSM.logger && window.HSM.logger.info(JSON.stringify({hose_env_config: window.ENV_CONFIG}), {log_name: 'hose-env-config'})
    }();
  </script>
  <script src="https://statics.ekuaibao.com/hose/html-template-script/localStorage-0.0.9.js"></script>
  <style type="text/css">
    #ekb-resload__section {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    #ekb-resload__button--restart {
      border: none;
      margin: 0px auto;
      width: 80px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      font-size: 14px;
      background: #2555FF;
      color: #FFFFFF;
      border-radius: 4px;
      cursor: pointer
    }

    #ekb-resload__canvas {
      width: 200px;
      height: 200px;
      background-image: url("data:image/svg+xml;base64,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");
      background-position: 50% 50%;
      background-repeat: no-repeat;
      background-size: 42px 48px;
    }
  </style>
  <script>
    window.__WHISPERED_PLUGINS__ = {};
    window.__webpack_public_path__ = '{{process_env_ASSET_PATH}}';
    (function () {
      function loadScript(src) {
        var script = document.createElement('script');
        script.id = 'MFE_REGISTRY_SCRIPT';
        script.type = 'text/javascript';
        script.async = false;
        script.defer = true;
        script.crossOrigin = 'use-credentials';
        script.setAttribute('data-loaded', 'loading');
        script.onerror = function (error) {
          script.setAttribute('data-loaded', 'error');
          throw new URIError('MFE.loadScript: The script ' + error.target.src + ' is not accessible.');
        };
        script.onload = function () {
          script.setAttribute('data-loaded', 'complete');
        };
        document.currentScript.parentNode.insertBefore(script, document.currentScript);
        script.src = src;
      }
      /**
       * @description: get corporation id form window location
       * @param {string} registryUrl
       * @return {void}
       */
      function getCorpId() {
        var part1 = location.search && location.search.split('corpId=')[1];
        var part2 = part1 && part1.split('&')[0];
        if (part2) {
          return part2;
        }
        var ekbCorpIds = location.search && location.search.split('ekbCorpId=')[1];
        return ekbCorpIds && ekbCorpIds.split('&')[0];
      }

      try {
        var corpId = getCorpId();
        var hostId = window.getEnvConfigValue('mfeHostId', 'b366e8bf-776d-4fb1-90cf-61aad1870496')
        var mfeUrl = window.getEnvConfigValue('mfeURL', 'https://mfe-server-dev.serverless.ekuaibao.com/api/registry/')
        loadScript(mfeUrl + hostId + '?corpId=' + corpId)
      } catch (ex) {
        console.error(ex);
      }
    }());
  </script>
  <script>
    !function () { "use strict"; var r = [{ regex: "^(Opera)/(\\d+)\\.(\\d+) \\(Nintendo Wii", family_replacement: "Wii", manufacturer: "Nintendo" }, { regex: "(SeaMonkey|Camino)/(\\d+)\\.(\\d+)\\.?([ab]?\\d+[a-z]*)", family_replacement: "Camino", other: !0 }, { regex: "(Pale[Mm]oon)/(\\d+)\\.(\\d+)\\.?(\\d+)?", family_replacement: "Pale Moon (Firefox Variant)", other: !0 }, { regex: "(Fennec)/(\\d+)\\.(\\d+)\\.?([ab]?\\d+[a-z]*)", family_replacement: "Firefox Mobile" }, { regex: "(Fennec)/(\\d+)\\.(\\d+)(pre)", family_replacment: "Firefox Mobile" }, { regex: "(Fennec)/(\\d+)\\.(\\d+)", family_replacement: "Firefox Mobile" }, { regex: "Mobile.*(Firefox)/(\\d+)\\.(\\d+)", family_replacement: "Firefox Mobile" }, { regex: "(Namoroka|Shiretoko|Minefield)/(\\d+)\\.(\\d+)\\.(\\d+(?:pre)?)", family_replacement: "Firefox ($1)" }, { regex: "(Firefox)/(\\d+)\\.(\\d+)(a\\d+[a-z]*)", family_replacement: "Firefox Alpha" }, { regex: "(Firefox)/(\\d+)\\.(\\d+)(b\\d+[a-z]*)", family_replacement: "Firefox Beta" }, { regex: "(Firefox)-(?:\\d+\\.\\d+)?/(\\d+)\\.(\\d+)(a\\d+[a-z]*)", family_replacement: "Firefox Alpha" }, { regex: "(Firefox)-(?:\\d+\\.\\d+)?/(\\d+)\\.(\\d+)(b\\d+[a-z]*)", family_replacement: "Firefox Beta" }, { regex: "(Namoroka|Shiretoko|Minefield)/(\\d+)\\.(\\d+)([ab]\\d+[a-z]*)?", family_replacement: "Firefox ($1)" }, { regex: "(Firefox).*Tablet browser (\\d+)\\.(\\d+)\\.(\\d+)", family_replacement: "MicroB", tablet: !0 }, { regex: "(MozillaDeveloperPreview)/(\\d+)\\.(\\d+)([ab]\\d+[a-z]*)?" }, { regex: "(Flock)/(\\d+)\\.(\\d+)(b\\d+?)", family_replacement: "Flock", other: !0 }, { regex: "(RockMelt)/(\\d+)\\.(\\d+)\\.(\\d+)", family_replacement: "Rockmelt", other: !0 }, { regex: "(Navigator)/(\\d+)\\.(\\d+)\\.(\\d+)", family_replacement: "Netscape" }, { regex: "(Navigator)/(\\d+)\\.(\\d+)([ab]\\d+)", family_replacement: "Netscape" }, { regex: "(Netscape6)/(\\d+)\\.(\\d+)\\.(\\d+)", family_replacement: "Netscape" }, { regex: "(MyIBrow)/(\\d+)\\.(\\d+)", family_replacement: "My Internet Browser", other: !0 }, { regex: "(Opera Tablet).*Version/(\\d+)\\.(\\d+)(?:\\.(\\d+))?", family_replacement: "Opera Tablet", tablet: !0 }, { regex: "(Opera)/.+Opera Mobi.+Version/(\\d+)\\.(\\d+)", family_replacement: "Opera Mobile" }, { regex: "Opera Mobi", family_replacement: "Opera Mobile" }, { regex: "(Opera Mini)/(\\d+)\\.(\\d+)", family_replacement: "Opera Mini" }, { regex: "(Opera Mini)/att/(\\d+)\\.(\\d+)", family_replacement: "Opera Mini" }, { regex: "(Opera)/9.80.*Version/(\\d+)\\.(\\d+)(?:\\.(\\d+))?", family_replacement: "Opera" }, { regex: "(OPR)/(\\d+)\\.(\\d+)(?:\\.(\\d+))?", family_replacement: "Opera" }, { regex: "(webOSBrowser)/(\\d+)\\.(\\d+)", family_replacement: "webOS" }, { regex: "(webOS)/(\\d+)\\.(\\d+)", family_replacement: "webOS" }, { regex: "(wOSBrowser).+TouchPad/(\\d+)\\.(\\d+)", family_replacement: "webOS TouchPad" }, { regex: "(luakit)", family_replacement: "LuaKit", other: !0 }, { regex: "(Lightning)/(\\d+)\\.(\\d+)([ab]?\\d+[a-z]*)", family_replacement: "Lightning", other: !0 }, { regex: "(Firefox)/(\\d+)\\.(\\d+)\\.(\\d+(?:pre)?) \\(Swiftfox\\)", family_replacement: "Swiftfox", other: !0 }, { regex: "(Firefox)/(\\d+)\\.(\\d+)([ab]\\d+[a-z]*)? \\(Swiftfox\\)", family_replacement: "Swiftfox", other: !0 }, { regex: "rekonq", family_replacement: "Rekonq", other: !0 }, { regex: "(conkeror|Conkeror)/(\\d+)\\.(\\d+)\\.?(\\d+)?", family_replacement: "Conkeror", other: !0 }, { regex: "(konqueror)/(\\d+)\\.(\\d+)\\.(\\d+)", family_replacement: "Konqueror", other: !0 }, { regex: "(WeTab)-Browser", family_replacement: "WeTab", other: !0 }, { regex: "(Comodo_Dragon)/(\\d+)\\.(\\d+)\\.(\\d+)", family_replacement: "Comodo Dragon", other: !0 }, { regex: "(YottaaMonitor)", family_replacement: "Yottaa Monitor", other: !0 }, { regex: "(Kindle)/(\\d+)\\.(\\d+)", family_replacement: "Kindle" }, { regex: "(Symphony) (\\d+).(\\d+)", family_replacement: "Symphony", other: !0 }, { regex: "Minimo", family_replacement: "Minimo", other: !0 }, { regex: "(Edge)/(\\d+)\\.(\\d+)", family_replacement: "Edge" }, { regex: "(CrMo)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)", family_replacement: "Chrome Mobile" }, { regex: "(CriOS)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)", family_replacement: "Chrome Mobile iOS" }, { regex: "(Chrome)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+) Mobile", family_replacement: "Chrome Mobile" }, { regex: "(chromeframe)/(\\d+)\\.(\\d+)\\.(\\d+)", family_replacement: "Chrome Frame" }, { regex: "(UC Browser)(\\d+)\\.(\\d+)\\.(\\d+)", family_replacement: "UC Browser", other: !0 }, { regex: "(SLP Browser)/(\\d+)\\.(\\d+)", family_replacement: "Tizen Browser", other: !0 }, { regex: "(Epiphany)/(\\d+)\\.(\\d+).(\\d+)", family_replacement: "Epiphany", other: !0 }, { regex: "(SE 2\\.X) MetaSr (\\d+)\\.(\\d+)", family_replacement: "Sogou Explorer", other: !0 }, { regex: "(Pingdom.com_bot_version_)(\\d+)\\.(\\d+)", family_replacement: "PingdomBot", other: !0 }, { regex: "(facebookexternalhit)/(\\d+)\\.(\\d+)", family_replacement: "FacebookBot" }, { regex: "(Twitterbot)/(\\d+)\\.(\\d+)", family_replacement: "TwitterBot" }, { regex: "(AdobeAIR|Chromium|FireWeb|Jasmine|ANTGalio|Midori|Fresco|Lobo|PaleMoon|Maxthon|Lynx|OmniWeb|Dillo|Camino|Demeter|Fluid|Fennec|Shiira|Sunrise|Chrome|Flock|Netscape|Lunascape|WebPilot|NetFront|Netfront|Konqueror|SeaMonkey|Kazehakase|Vienna|Iceape|Iceweasel|IceWeasel|Iron|K-Meleon|Sleipnir|Galeon|GranParadiso|Opera Mini|iCab|NetNewsWire|ThunderBrowse|Iron|Iris|UP\\.Browser|Bunjaloo|Google Earth|Raven for Mac)/(\\d+)\\.(\\d+)\\.(\\d+)" }, { regex: "(Bolt|Jasmine|IceCat|Skyfire|Midori|Maxthon|Lynx|Arora|IBrowse|Dillo|Camino|Shiira|Fennec|Phoenix|Chrome|Flock|Netscape|Lunascape|Epiphany|WebPilot|Opera Mini|Opera|NetFront|Netfront|Konqueror|Googlebot|SeaMonkey|Kazehakase|Vienna|Iceape|Iceweasel|IceWeasel|Iron|K-Meleon|Sleipnir|Galeon|GranParadiso|iCab|NetNewsWire|Iron|Space Bison|Stainless|Orca|Dolfin|BOLT|Minimo|Tizen Browser|Polaris)/(\\d+)\\.(\\d+)" }, { regex: "(iRider|Crazy Browser|SkipStone|iCab|Lunascape|Sleipnir|Maemo Browser) (\\d+)\\.(\\d+)\\.(\\d+)" }, { regex: "(iCab|Lunascape|Opera|Android|Jasmine|Polaris|BREW) (\\d+)\\.(\\d+)\\.?(\\d+)?" }, { regex: "(IEMobile)[ /](\\d+)\\.(\\d+)", family_replacement: "IE Mobile" }, { regex: "(MSIE) (\\d+)\\.(\\d+).*XBLWP7", family_replacement: "IE Large Screen" }, { regex: "(Firefox)/(\\d+)\\.(\\d+)\\.(\\d+)" }, { regex: "(Firefox)/(\\d+)\\.(\\d+)(pre|[ab]\\d+[a-z]*)?" }, { regex: "(MAXTHON|Maxthon) (\\d+)\\.(\\d+)", family_replacement: "Maxthon", other: !0 }, { regex: "(Maxthon|MyIE2|Uzbl|Shiira)", v1_replacement: "0", other: !0 }, { regex: "(BrowseX) \\((\\d+)\\.(\\d+)\\.(\\d+)", other: !0 }, { regex: "(POLARIS)/(\\d+)\\.(\\d+)", family_replacement: "Polaris", other: !0 }, { regex: "(Embider)/(\\d+)\\.(\\d+)", family_replacement: "Polaris", other: !0 }, { regex: "(iPod).+Version/(\\d+)\\.(\\d+)\\.(\\d+)", family_replacement: "Mobile Safari", manufacturer: "Apple" }, { regex: "(iPod).*Version/(\\d+)\\.(\\d+)", family_replacement: "Mobile Safari", manufacturer: "Apple" }, { regex: "(iPod)", family_replacement: "Mobile Safari", manufacturer: "Apple" }, { regex: "(iPhone).*Version/(\\d+)\\.(\\d+)\\.(\\d+)", family_replacement: "Mobile Safari", manufacturer: "Apple" }, { regex: "(iPhone).*Version/(\\d+)\\.(\\d+)", family_replacement: "Mobile Safari", manufacturer: "Apple" }, { regex: "(iPhone)", family_replacement: "Mobile Safari", manufacturer: "Apple" }, { regex: "(iPad).*Version/(\\d+)\\.(\\d+)\\.(\\d+)", family_replacement: "Mobile Safari", tablet: !0, manufacturer: "Apple" }, { regex: "(iPad).*Version/(\\d+)\\.(\\d+)", family_replacement: "Mobile Safari", tablet: !0, manufacturer: "Apple" }, { regex: "(iPad)", family_replacement: "Mobile Safari", tablet: !0, manufacturer: "Apple" }, { regex: "(AvantGo) (\\d+).(\\d+)", other: !0 }, { regex: "(Avant)", v1_replacement: "1", other: !0 }, { regex: "(OmniWeb)/v(\\d+)\\.(\\d+)", other: !0 }, { regex: "(Blazer)/(\\d+)\\.(\\d+)", family_replacement: "Palm Blazer", manufacturer: "Palm" }, { regex: "(Pre)/(\\d+)\\.(\\d+)", family_replacement: "Palm Pre", manufacturer: "Palm" }, { regex: "(Links) \\((\\d+)\\.(\\d+)", other: !0 }, { regex: "(QtWeb) Internet Browser/(\\d+)\\.(\\d+)", other: !0 }, { regex: "(Silk)/(\\d+)\\.(\\d+)(?:\\.([0-9\\-]+))?", other: !0, tablet: !0 }, { regex: "(AppleWebKit)/(\\d+)\\.?(\\d+)?\\+ .* Version/\\d+\\.\\d+.\\d+ Safari/", family_replacement: "WebKit Nightly" }, { regex: "(Version)/(\\d+)\\.(\\d+)(?:\\.(\\d+))?.*Safari/", family_replacement: "Safari" }, { regex: "(Safari)/\\d+" }, { regex: "(OLPC)/Update(\\d+)\\.(\\d+)", other: !0 }, { regex: "(OLPC)/Update()\\.(\\d+)", v1_replacement: "0", other: !0 }, { regex: "(SEMC\\-Browser)/(\\d+)\\.(\\d+)", other: !0 }, { regex: "(Teleca)", family_replacement: "Teleca Browser", other: !0 }, { regex: "Trident(.*)rv.(\\d+)\\.(\\d+)", family_replacement: "IE" }, { regex: "(MSIE) (\\d+)\\.(\\d+)", family_replacement: "IE" }]; function a(e) { return function (e, r) { for (var a = {}, i = 0; i < r.length && !(a = r[i](e)); i++); return a }(e, r.map(function (e) { var i = new RegExp(e.regex), o = e.family_replacement; return function (e) { var r = e.match(i); if (!r) return null; var a = {}; return a.family = (o ? o.replace("$1", r[1]) : r[1]) || "other", a.major = parseInt(r[2]) || null, a.minor = r[3] ? parseInt(r[3]) : null, a.patch = r[4] ? parseInt(r[4]) : null, a } })) } function i(e, r) { var a = document.createElement("script"); a.src = e, a.onload = function () { r && r() }, a.onerror = function () { r && r(new Error("Failed to load script " + e)) }, document.head.appendChild(a) } var e = { isNeedPatchUseUserAgent: function (e) { var r = a(e); return !(r && r.family && r.major) || ("IE" === r.family || !(r.family.includes("Chrome") && 60 <= r.major || r.family.includes("Firefox") && 45 <= r.major || r.family.includes("Safari") && 11 <= r.major || r.family.includes("Opera") && 47 <= r.major || r.family.includes("Edge") && 15 <= r.major)) }, isNeedPatchUseBuiltIn: function () { return !(Array.prototype.forEach && Array.prototype.map && Array.prototype.find && String.prototype.includes) || (!(window.Promise && window.fetch && window.Symbol && window.EventSource) || void 0) }, parseUA: a }, o = e.isNeedPatchUseBuiltIn, n = e.isNeedPatchUseUserAgent; window.__PATCH_POLYFILL__ = function (e, r) { return !0 === o() || !0 === n(window.navigator.userAgent) ? i(e, r) : void 0 } }();
  </script>
  <script>
    window.__PATCH_POLYFILL__("{{webpackConfig.output.publicPath}}vendor-polyfill.1.0.3.min.js");
  </script>
</head>

<body focusable="true" id="web">
  <!-- resource loading animation dom -->
  <div id="ekb-resload__section">
    <!-- forbid setting canvas attribute width and height, instead of setting in style -->
    <!-- setting the width and height of canvas can cause high dpi dynamic adaptation fail  -->
    <canvas id="ekb-resload__canvas"></canvas>
    <button id="ekb-resload__button--restart" onclick="window.location.reload(true)" style="display: none">
      点击重试
    </button>
  </div>
  <!-- pre resource loading logic -->
  <script>
    !function (t, e) { "object" == typeof exports && "undefined" != typeof module ? module.exports = e() : "function" == typeof define && define.amd ? define(e) : (t = "undefined" != typeof globalThis ? globalThis : t || self).ekbLoader = e() }(this, (function () { "use strict"; return function () { function t() { } return t.setupCanvas = function (t, e) { e && (this.total = e + 1), this.ctx = function (t) { try { if (t) { var e = window.devicePixelRatio || 1, i = (null == t ? void 0 : t.getBoundingClientRect()) || 1; t.width = i.width * e, t.height = i.height * e; var n = t.getContext("2d"); return n.scale(e, e), n } } catch (t) { console.log("setup canvas error===", t) } }(t), this.drawCircleLoading(this.ctx, 0) }, t.showRetryButton = function () { var t; null !== (t = document.getElementById("ekb-resload__button--restart") || null) && "none" === t.style.display && (t.style.display = "block") }, t.drawCircleLoading = function (t, e) { return function (t, e) { try { t && (window.devicePixelRatio, t.clearRect(0, 0, 200, 200), t.beginPath(), t.lineWidth = 2, t.strokeStyle = "#4C79FF", t.arc(100, 100, 40, 0, 2 * Math.PI, !1), t.stroke(), t.beginPath(), t.lineWidth = 2, t.strokeStyle = "rgba(50,207,224,1)", t.arc(100, 100, 50, 0, 2 * Math.PI, !1), t.stroke(), t.beginPath(), t.lineWidth = 2, t.strokeStyle = "#F5F5F5", t.arc(100, 100, 50, 0, 2 * Math.PI, !1), t.stroke(), t.beginPath(), t.save(), t.translate(0, 200), t.rotate(270 * Math.PI / 180), t.lineWidth = 2, t.strokeStyle = "#F5F5F5", t.arc(100, 100, 40, 0, 1.9999 * Math.PI * (.01 * Math.max(e, .01)), !0), t.stroke(), t.restore(), t.beginPath(), t.font = "14px sans-serif", t.fillStyle = "#4C79FF", t.fillText(e + "%", 100 - t.measureText(e + "%").width / 2, 186), t.fill(), t.closePath()) } catch (t) { console.log("draw canvas loading error===", t) } }(t, e) }, t.resErrorReport = function (t) { this.isPaused = !0, this.showRetryButton() }, t.setExtendRes = function (t) { t > 0 && (this.mfeTotal = t, this.total = this.total + t) }, t.resLoaded = function (t, e) { if (!this.isPaused) { if (void 0 === t) { this.current += 1; var i = Math.min(Math.floor(this.current / this.total * 100 * .99), 99); this.drawCircleLoading(this.ctx, i) } if (t) { var n = e, o = n.type, r = n.state; if (1 === o && 2 === r) { this.current += 1; i = 99 + Math.min(Number(((this.current - (this.total - this.mfeTotal)) / this.mfeTotal).toFixed(2)), 1); this.drawCircleLoading(this.ctx, i) } else this.resErrorReport("mfe loaded error") } } }, t.total = 0, t.current = 0, t.mfeTotal = 0, t.isPaused = !1, t.ctx = null, t }() }));

    (function () {
      // pre resource loading animaition inline javascript
      ekbLoader.setupCanvas(document.getElementById("ekb-resload__canvas"), {{ resourceAmount }});;
    }) ();
  </script>
</body>

</html>