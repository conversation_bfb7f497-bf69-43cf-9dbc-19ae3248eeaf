/**************************************************
 * Created by nany<PERSON>ingfeng on 11/07/2017 17:34.
 **************************************************/
import styles from './RightPartFooter.module.less'
import React, { PureComponent } from 'react'
import { ButtonGroup } from '@ekuaibao/eui'
import { EnhanceConnect } from '@ekuaibao/store'
import { commentFlow, delFlow, retractFlow, checkYegoOrder, calPlanInstance, nullifyFlow } from '../../../bills.action'
import { get, isObject, forEach, debounce } from 'lodash'
import { showMessage, showModal } from '@ekuaibao/show-util'
import { app as api } from '@ekuaibao/whispered'
import {
  getValidateErrorByShow,
  setFormMultiplePayeesMode,
  getDefSpecificationDS,
  formatCopyBillData,
  confirmCopy,
  fnPushDingTalkShareBtn,
  checkSpecificationActive,
  updateFormData,
  shareBillAction,
  fnFormatMoneyValue
} from '../../../util/billUtils'
import { isFunction } from '@ekuaibao/helpers'
import EKBIcon from '../../../../../elements/ekbIcon'
import { related } from '../../../../../elements/feeDetailViewList/Related'
import { isPrintShow } from '../../../util/fnCheckPrintAvailable'
import { Fetch } from '@ekuaibao/fetch'
import { TITLE_CONFIG } from '../../../util/config'
import { validTripSync, getTripSyncStatus, handleSynchronous } from './validateTripSync'
import UncontrolledLottie from '../../../../../elements/Animation/UncontrolledLottie'
import { Modal } from 'antd'
import Header from '../../../layers/add-bill-fullscreen/views/header'
import { message } from '@hose/eui'
import { SkeletonButton } from './SkeletonModal'
import BillInfoButtons from '../../../elements/BillInfoButtons'
import { FlowAction } from '../../../layers/bill-info-popup/FlowAction'
import {
  startSaveFlowPerformanceStatistics,
  endFlowFormDataCollectionStatistics
} from '../../../../../lib/flowPerformanceStatistics'
import { getBoolVariation } from '../../../../../lib/featbit'
import { FlexableButtonGroup } from '../../../../../elements/flexable-button-group'
@EnhanceConnect(
  state => ({
    CHANGJIEPay: state['@common'].powers.CHANGJIEPay,
    remunerationBatchField: state['@remuneration'].remunerationBatchField,
    isShowRemunerationTab: state['@remuneration'].isShowRemunerationTab,
    baseDataProperties: state['@common'].globalFields.data,
    globalFieldsMap: state['@common'].globalFields.baseDataPropertiesMap,
    userInfo: state['@common'].userinfo.data,
    autoExpenseWithBillStriction: state['@common'].powers.autoExpenseWithBillStriction,
    specificationGroupsList: state['@custom-specification'].specificationGroupsList
  }),
  {
    delFlow,
    retractFlow,
    commentFlow,
    nullifyFlow,
    calPlanInstance
  }
)
export default class RightPartFooter extends PureComponent {
  constructor(props, ...args) {
    super(props, ...args)
    this.bus = props.bus
    this.draftModal = null
    this.loadingCount = 0 // 新增
    this.state = {
      isQuickExpends: false,
      saveLoading: false,
      submitLoading: false,
      disabled: false,
      lastTime: 0,
      countAmount: 0,
      showPrintBtn: false,
      flowInfo: null
    }

    this.containerRef = React.createRef()
  }

  componentWillMount() {
    this.bus.on('footer:action:save', this.handleSave)
    this.bus.on('footer:action:submit', this.handleSubmit)
    this.bus.on('savebtn:state:change', this.loadingChange)
    this.bus.on('clear:status', this.clearStatus)
    this.bus.on('footer:notifycheck:pending:copy', this.handleCheckPendingCopy)
    this.bus.watch('get:footer:action:state', this.handleGetFooterActionState)
    api.watch('change:bills:isQuickExpends', this.handleChangeBillsIsQuickExpends)
    api.on('update:bill:list', this.handleUpdateBill)
    window.addEventListener('resize', this.resizeHandler)
  }

  resizeHandler = debounce(() => {
    this.forceUpdate()
  }, 100)

  componentWillUnmount() {
    this.bus.un('footer:action:save', this.handleSave)
    this.bus.un('footer:action:submit', this.handleSubmit)
    this.bus.un('savebtn:state:change', this.loadingChange)
    this.bus.on('clear:status', this.clearStatus)
    this.bus.un('footer:notifycheck:pending:copy', this.handleCheckPendingCopy)
    this.bus.un('get:footer:action:state', this.handleGetFooterActionState)
    api.un('change:bills:isQuickExpends', this.handleChangeBillsIsQuickExpends)
    api.un('update:bill:list', this.handleUpdateBill)
    window.removeEventListener('resize', this.resizeHandler)
  }

  handleChangeBillsIsQuickExpends = isQuickExpends => {
    this.setState({ isQuickExpends })
    // 获取打印模板是否配置跟随发票
    this.getPrintInvoice()
  }

  handleUpdateBill = flowInfo => {
    this.setState({ flowInfo })
  }

  componentWillReceiveProps(nextProps) {
    this.setState({ lastTime: 0 })
  }

  getPrintInvoice = () => {
    const { dataSource, form } = this.props
    const id = dataSource || form ? get(dataSource, 'id') || form?.id : ''
    if (id) {
      api.invokeService('@bills:get:show:print:invoice', { flowId: id }).then(data => {
        this.setState({ showPrintBtn: data?.value || false })
      })
    }
  }
  handleGetFooterActionState = () => {
    const { submitLoading, saveLoading, disabled } = this.state
    return submitLoading || saveLoading || disabled
  }

  loadingChange = ({ disabled }) => {
    if (getBoolVariation('cyxq-75301')) {
      if (disabled) {
        this.loadingCount += 1
        if (!this.state.disabled) {
          this.setState({ disabled: true })
        }
      } else {
        this.loadingCount = Math.max(0, this.loadingCount - 1)
        if (this.loadingCount === 0 && this.state.disabled) {
          this.setState({ disabled: false })
        }
      }
      return
    }
    const d = this.state.disabled
    if (d === disabled) return
    this.setState({ disabled: disabled })
  }
  clearStatus = () => {
    this.loadingCount = 0
    this.setState({
      disabled: false,
      saveLoading: false,
      submitLoading: false
    })
  }

  __handleInsertAssist = title => {
    api.invokeService('@common:insert:assist:record', {
      title
    })
  }

  showdraftModal = () => {
    this.draftModal = Modal.info({
      title: '',
      okText: '',
      cancelText: '',
      keyboard: false,
      className: 'draft-modal-layer',
      content: (
        <div className="draft-modal-layer">
          <UncontrolledLottie />
          <p>行程同步中，请稍后...</p>
        </div>
      )
    })
  }

  queryDraftResult = async count => {
    if (!this.draftModal) this.showdraftModal()
    let tripList = await this.props.bus.invoke('get:tripdatalink:value')
    if (tripList?.length) {
      let result = await getTripSyncStatus(this.props)
      let count = this.state.countAmount
      if (result === 'PENDING' && count < 4) {
        this.setState({ countAmount: ++count })
        return false
      } else {
        this.draftModal.destroy()
        this.draftModal = null
        return true
      }
    } else {
      this.draftModal.destroy()
      return true
    }
  }

  handleSave = async obj => {
    if (this.bus.$isTableEdit) {
      return message.info(i18n.get('保存完当前编辑的费用再进行其他操作'))
    }
    startSaveFlowPerformanceStatistics()
    let isEnableDraftConfig = await this.props.bus.invoke('get:enableDraftConfig:value')
    if (isEnableDraftConfig) {
      let result = await this.queryDraftResult()
      if (!result) {
        setTimeout(async () => {
          this.handleSave()
        }, 1500)
        return
      } else {
        this.setState({ countAmount: 0 })
      }
    }

    const { scanViewData, dataSource, setValidateError, from } = this.props
    this.setState({ saveLoading: true, disabled: true }, () => {
      setTimeout(() => {
        if (!isFunction(this.bus?.getValueWithValidate)) return
        this.bus
          .getValueWithValidate(1)
          .catch(e => {
            const components =
              get(dataSource, 'currentSpecification.components') || get(dataSource, 'form.specificationId.components')
            const errors = getValidateErrorByShow(components, Object.keys(e))
            if (!errors.length) {
              setValidateError({ bill: Object.keys(e) })
            }
            this.tempPendingCopy && delete this.tempPendingCopy
            throw e
          })
          .then(this.setRemunerationBatchId)
          .then(formValue => {
            const specification = get(dataSource, 'currentSpecification') || get(dataSource, 'form.specificationId')
            return fnFormatMoneyValue({ data: formValue, specification })
          })
          // .then(this.checkApportions)
          .then(formValue => {
            this.setState({ saveLoading: true, disabled: true })
            setFormMultiplePayeesMode(formValue, dataSource)
            formValue.title && this.__handleInsertAssist(`保存${formValue.title}单据`) // @i18n-ignore
            updateFormData(dataSource, formValue)
            if (this.bus.$showType === 'TABLE_NO_GROUP') {
              // 费用类型表格编辑埋点
              api?.logger.info(`费用类型表格编辑保存草稿单据埋点`, { formValue })
            }
            endFlowFormDataCollectionStatistics()
            return this.bus.invoke('save:bill:click', formValue, obj, this.onCloseModal)
          })
          .then(async res => {
            // 修复无权限复制时，进入到新的单据时，还会调用复制
            if (!res) {
              this.tempPendingCopy = false
              this.props.onPengdingCopy?.(false)
            }
            const { state, current } = res || {}
            const { formType } = dataSource
            const supplierBills = ['reconciliation', 'settlement']
            const isRouteReconciliation = location.hash.endsWith('/reconciliation')
            // 增加自动同步行程
            let isEnableDraftConfig = await this.props.bus.invoke('get:enableDraftConfig:value')
            if (isEnableDraftConfig) {
              const hasTrip = get(current, 'flow.form.u_行程规划')?.length ? true : false
              if (hasTrip) {
                setTimeout(() => {
                  handleSynchronous(current?.id, 'freeflow.edit')
                }, 3000)
              }
            }
            if ((supplierBills.includes(formType) && isRouteReconciliation) || this.bus.$_fromPlanQuery) {
              if (state === 'success') {
                setTimeout(() => {
                  formType === 'reconciliation' && showMessage.info(i18n.get('对账单已保存，请在对账中列表查看'))
                  formType === 'settlement' && showMessage.info(i18n.get('结算单已保存，请在结算中列表查看'))
                  api.emit('update:reconiliation:data') // 延迟刷新带对账数据
                  this.setState({ saveLoading: false, disabled: false })
                  scanViewData && scanViewData.fn()
                }, 3000)
              }
            } else {
              scanViewData && scanViewData.fn()
              this.setState({ saveLoading: false, disabled: false })
            }
            if (from === 'from_myBill') {
              this.tempPendingCopy && this.handleCheckPendingCopy()
            }
            setValidateError({ bill: [], detail: [], trip: {} })
            obj?.checkValueCallback?.()
          })
          .catch(e => {
            //校验失败
            this.tempPendingCopy && delete this.tempPendingCopy
            this.setState({ saveLoading: false, disabled: false })
            if (e && (e['details'] || e['trips'])) {
              let { errors = [] } = e['details'] || e['trips']
              let { message } = errors[0]
              showMessage.error(message)
            }
          })
      }, 100)
    })
  }

  // 获取当前单据模板的originalId
  getCurrencySpecOriginalId = () => {
    let { dataSource } = this.props
    let { form = {}, currentSpecification, requisitionInfo } = dataSource
    const specification = currentSpecification || form.specificationId || requisitionInfo?.defaultSpecification || {}
    return get(specification, 'originalId')
  }

  //草稿态 驳回态 增加复制action，复制前需先 保存草稿
  handleSaveAndCopy = async () => {
    // 单据使用的模板被停用时，拦截复制动作
    const specOriginalId = this.getCurrencySpecOriginalId()
    const specActive = await checkSpecificationActive(specOriginalId)
    if (!specActive) return

    this.tempPendingCopy = true
    this.props.onPengdingCopy?.(true)
    this.handleSave()
    if (this.props?.from === 'from_drawer') {
      this.handleCopy()
    }
  }
  handleCheckPendingCopy = () => {
    if (!this.tempPendingCopy) return
    const { from, fnGetBillNewData } = this.props
    if (from === 'from_myBill' && fnGetBillNewData) {
      fnGetBillNewData().then(data => {
        data?.value && this.handleDrawerCopy(data.value)
      })
    } else {
      this.handleCopy()
    }
    this.tempPendingCopy = false
    this.props.onPengdingCopy?.(false)
  }
  setRemunerationBatchId = formValue => {
    const { remunerationBatchField, dataSource, isShowRemunerationTab } = this.props
    return api.invokeService('@remuneration:set:batchId', {
      formValue,
      remunerationBatchField,
      dataSource,
      isShowRemunerationTab
    })
  }

  handleSubmit = async () => {
    if (this.bus.$isTableEdit) {
      return message.info(i18n.get('保存完当前编辑的费用再进行其他操作'))
    }
    const { scanViewData, dataSource = {}, setValidateError, specificationGroupsList } = this.props
    startSaveFlowPerformanceStatistics()
    if (specificationGroupsList && specificationGroupsList?.length === 0) {
      await api.invokeService('@custom-specification:get:specificationGroups:withSpecificationVersioned')
    }
    const { submitLoading, saveLoading, disabled } = this.state
    if (submitLoading || saveLoading || disabled) return
    const curSpecification = getDefSpecificationDS(dataSource, this.props.specificationGroupsList)
    if (curSpecification?.mustUpdateTemplate && !curSpecification?.active) {
      showModal.error({ title: i18n.get('未更新模板'), content: i18n.get('此单据未更新模板,无法提交') })
      return
    }
    //单据上有hab组件需要做hab里的表单校验
    let habError = []
    if (this.bus?.euiHabForms) {
      const validateAndHandleErrors = async habform => {
        await habform.validateFields().catch(err => {
          habError.push({ habform, err })
        })
      }
      await Promise.all(Object.values(this.bus.euiHabForms).map(habform => habform && validateAndHandleErrors(habform)))
    }
    if (habError.length) {
      const { habform, err } = habError[0]
      habform.scrollToField(err.errorFields[0].name[0])
      showMessage.error(err.errorFields[0].errors[0])
      return
    }
    isFunction(this.bus?.getValueWithValidate) &&
      setTimeout(() => {
        this.bus
          .getValueWithValidate(0)
          .catch(e => {
            const components =
              get(dataSource, 'currentSpecification.components') || get(dataSource, 'form.specificationId.components')
            const errors = getValidateErrorByShow(components, Object.keys(e))
            if (!errors.length) {
              setValidateError({ bill: Object.keys(e) })
            }
            throw e
          })
          .then((formValue) => {
            if (!getBoolVariation('ao-84-optimize-datalinkedittable', false)) {
              return Promise.resolve(formValue)
            }
            const components =
              get(dataSource, 'currentSpecification.components') || get(dataSource, 'form.specificationId.components') || []
            const dataLinkEdits = components.filter(v => v.type === 'dataLinkEdits' && v.showType === 'TABLE' && ['INSERT', 'UPDATE', 'MORE'].includes(v.behaviour))
            return Promise.all(dataLinkEdits.map(field => {
              if (this.bus.has(`submit:bill:click:validate:${field?.field}`)) {
                return this.bus.invoke(`submit:bill:click:validate:${field?.field}`)
              }
              return Promise.resolve()
            })).then(res => {
              if (res.some(v => v === 'failed')) {
                return Promise.reject(new Error('提交失败'))
              }
              return Promise.resolve(formValue)
            })
          })
          .then(this.checkTrips)
          .then(this.checkLedgerCalcEnd)
          .then(this.checkYegoOrder)
          .then(formValue => {
            const specification = get(dataSource, 'currentSpecification') || get(dataSource, 'form.specificationId')
            return fnFormatMoneyValue({ data: formValue, specification })
          })
          .then(formValue => {
            setFormMultiplePayeesMode(formValue, dataSource)
            updateFormData(dataSource, formValue)
            this.setState({ submitLoading: true, disabled: true })
            this.__handleInsertAssist(`提交${formValue.title}单据`) // @i18n-ignore
            if (this.bus.$showType === 'TABLE_NO_GROUP') {
              // 费用类型表格编辑埋点
              api?.logger.info(`费用类型表格编辑提交单据埋点`, { formValue })
            }
            endFlowFormDataCollectionStatistics()
            return this.bus.invoke('submit:bill:click', formValue)
          })
          .then(async res => {
            const { state, id, params, current } = res || {}
            const { formType } = dataSource
            const supplierBills = ['reconciliation', 'settlement', 'payment']
            const isRouteReconciliation = location.hash.endsWith('/reconciliation')
            const flowState = get(current, 'flow.state')
            // 增加自动同步行程
            let isEnableDraftConfig = await this.props.bus.invoke('get:enableDraftConfig:value')
            if (isEnableDraftConfig) {
              const hasTrip = get(current, 'flow.form.u_行程规划')?.length ? true : false
              if (hasTrip) handleSynchronous(id, 'freeflow.submit')
            }
            if ((supplierBills.includes(formType) && isRouteReconciliation) || this.bus.$_fromPlanQuery) {
              if (state === 'success') {
                api.emit('update:reconiliation:data')
                scanViewData && scanViewData.fn()
                this.onCloseModal(true)
              } else {
                api.emit('update:reconiliation:bill', { ...params, id, state: 'draft' })
              }
            } else {
              scanViewData && scanViewData.fn(res) // 弹窗关闭
              if (state === 'success' || flowState === 'pending') {
                this.onCloseModal(true)
              }
            }
            this.setState({ submitLoading: false, disabled: false })
            setValidateError({ bill: [], detail: [], trip: {} })
          })
          .catch(e => {
            //校验失败
            this.setState({ submitLoading: false, disabled: false })
            if (e && (e['details'] || e['trips'] || e['remuneratin'])) {
              let { errors = [] } = e['details'] || e['trips'] || e['remuneratin']
              let { message } = errors[0]
              showMessage.error(message)
            }
          })
      }, 500)
  }

  checkYegoOrder = formValue => {
    const { expenseLink, details } = formValue
    const isYeego = api.getState('@common').powers.YEEGO
    if (expenseLink && isYeego) {
      return new Promise((resolve, reject) => {
        checkYegoOrder({
          expenseLink: isObject(expenseLink) ? expenseLink.id : expenseLink,
          details
        }).then(data => {
          if (data.value) {
            resolve(formValue)
          } else {
            showModal.confirm({
              title: i18n.get('单据关联的申请存在订单未导入,是否提交?'),
              onOk: () => {
                resolve(formValue)
              },
              onCancel: () => {
                reject()
              }
            })
          }
        })
      })
    }
    return formValue
  }

  checkApportions = formValue => {
    return new Promise((resolve, reject) => {
      const details = get(formValue, 'details')
      details &&
        details.forEach(v => {
          if (v.isRemuneration) {
            return
          }
          const {
            specificationId: { components },
            feeTypeForm
          } = v
          const { open } = components.find(v => v.type === 'apportions') || {}
          if (open && (!feeTypeForm.apportions || !feeTypeForm.apportions.length)) {
            return reject({ details: { errors: [{ message: i18n.get('消费明细分摊未填写') }] } })
          }
        })
      return resolve(formValue)
    })
  }
  checkLedgerCalcEnd = formValue => {
    const { isShowRemunerationTab } = this.props
    return api.invokeService('@remuneration:check:ledger:calcEnd', {
      props: this.props,
      formValue,
      isShowRemunerationTab
    })
  }
  checkTrips = formValue => {
    return new Promise((resolve, reject) => {
      const trips = get(formValue, 'trips', [])

      if (trips.length === 0) return resolve(formValue)
      for (let item of trips) {
        const {
          specificationId: { components },
          tripForm
        } = item
        for (let i of components) {
          const { optional, field } = i
          if (optional !== undefined && !optional && !tripForm[field]) {
            return reject({ trips: { errors: [{ message: i18n.get('行程信息填写不完整') }] } })
          }
        }
      }
      return resolve(formValue)
    })
  }

  reloadData = () => {
    api.invokeService('@layout5:refresh:menu:data')
  }

  onCloseModal = (refresh = true) => {
    const { from, closeDrawer, onCloseModal, callback, handleCreatePopupSave } = this.props
    onCloseModal && onCloseModal()
    refresh && callback && callback()
    handleCreatePopupSave?.(refresh)
    if (from === 'from_drawer') {
      closeDrawer && closeDrawer()
    }
    if (from === 'from_myBill') {
      closeDrawer && closeDrawer(!this.tempPendingCopy)
    }
  }

  handleDel = async () => {
    let isEnableDraftConfig = await this.props.bus.invoke('get:enableDraftConfig:value')
    if (isEnableDraftConfig) {
      let res_TripSync = await validTripSync(this.props)
      if (!res_TripSync) return
    }

    const { dataSource } = this.props
    const baseType = ['settlement', 'reconciliation']
    this.props?.bus?.getValue().then(async value => {
      const details = get(value, 'details')
      const feeData = details ? await this.props.bus.invoke('get:moveTo:RecordExpendsDetails') : []
      const cDetails = !!details ? details : []
      const data = cDetails.concat(feeData)
      const billType = get(dataSource, 'formType')
      const amountList = !!data.length ? data.filter(v => get(v, 'feeTypeForm.amount.standard')) : []
      !!amountList.length && !baseType.includes(billType)
        ? this.fnOpenDeleteBillModal(amountList)
        : this.fnDeleteBill(billType)
    })
  }

  fnOpenDeleteBillModal = async amountList => {
    const { autoExpenseWithBillStriction, baseDataProperties, dataSource, userInfo } = this.props
    const { isQuickExpends } = this.state
    const billFormType = get(dataSource, 'formType')
    const ownerId = get(dataSource, 'ownerId.id') || get(userInfo, 'staff.id')
    const submitterId = get(dataSource, 'form.submitterId.id')
    const id = get(dataSource, 'id')
    const details = get(dataSource, 'form.details')
    const code = get(dataSource, 'form.code')
    const specificationId = get(dataSource, 'form.specificationId.id')
    api
      .open('@bills:DeleteBillModal', {
        amountList,
        billFormType,
        baseDataProperties,
        ownerId,
        flowId: id,
        details,
        isQuickExpends,
        code,
        specificationId,
        submitterId,
        autoExpenseWithBillStriction
      })
      .then(res => {
        this.fnDeleteFinish()
        // 判断是否有权限删除并移动到随手记
        const { staff } = userInfo
        const canMoveToNote = staff?.id === ownerId ? true : false
        if (res.value === 'deleteAndMove' && canMoveToNote) {
          const message = isQuickExpends
            ? i18n.get('费用已被移至「快速报销」，您可在「快速报销」中继续编辑')
            : i18n.get('费用已被移至「随手记」，您可在「随手记」中继续编辑')
          showMessage.info(message)
          this.onCloseModal(true)
          this.bus.emit('update:bill:list', undefined, undefined, { action: FlowAction.Delete })
        }
      })
  }

  fnDeleteBill = type => {
    const label = i18n.get(TITLE_CONFIG[type])
    showModal.confirm({
      title: i18n.get('你确定删除单据吗？'),
      content: i18n.get('bot-been-del', { label }),
      okText: i18n.get('删除'),
      cancelText: i18n.get('取消'),
      onOk: this.fnDeleteFinish
    })
  }

  fnDeleteFinish = () => {
    related.clearRelatedData()
    let { delFlow, dataSource } = this.props
    const { flowInfo } = this.state
    let id = get(dataSource, 'id')
    const title = get(dataSource, 'form.title') || ''
    let _this = this
    if (!id && dataSource?.state !== 'new') {
      id = flowInfo?.id
    }
    if (id) {
      delFlow(id).then(_ => {
        _this.bus.emit('update:bill:list', undefined, undefined, { action: FlowAction.Delete })
        _this.__handleInsertAssist(`删除${title}单据`) // @i18n-ignore
        this.onCloseModal(true)
      })
    } else {
      _this.bus.emit('list:select:change')
      this.onCloseModal(true)
    }
  }

  handleNullify = async () => {
    let isEnableDraftConfig = await this.props.bus.invoke('get:enableDraftConfig:value')
    if (isEnableDraftConfig) {
      let res_TripSync = await validTripSync(this.props)
      if (!res_TripSync) return
    }

    const { dataSource } = this.props
    api
      .invokeService('@bills:get:check:nulllify:flowId', { flowId: dataSource.id || dataSource.form?.id })
      .then(res => {
        if (res.value) {
          const baseType = ['settlement', 'reconciliation']
          this.props?.bus?.getValue().then(async value => {
            const details = get(value, 'details')
            const feeData = details ? await this.props.bus.invoke('get:moveTo:RecordExpendsDetails') : []
            const cDetails = !!details ? details : []
            const data = cDetails.concat(feeData)
            const billType = get(dataSource, 'formType')
            const amountList = !!data.length ? data.filter(v => get(v, 'feeTypeForm.amount.standard')) : []
            !!amountList.length && !baseType.includes(billType)
              ? this.fnOpenNullifyBillModal(amountList)
              : this.fnNullifyBill()
          })
        } else {
          showMessage.info(res.message)
        }
      })
  }
  fnOpenNullifyBillModal = async amountList => {
    const { baseDataProperties, dataSource, userInfo } = this.props
    const { isQuickExpends } = this.state
    const billFormType = get(dataSource, 'formType')
    const ownerId = get(dataSource, 'ownerId.id') || get(userInfo, 'staff.id')
    const submitterId = get(dataSource, 'form.submitterId.id')
    const id = get(dataSource, 'id')
    const details = get(dataSource, 'form.details')
    api
      .open('@bills:NullifyBillModal', {
        amountList,
        billFormType,
        baseDataProperties,
        ownerId,
        flowId: id,
        details,
        isQuickExpends,
        submitterId
      })
      .then(res => {
        this.fnNullifyFinish()
        if (res.value === 'nullifyAndMove') {
          const message = isQuickExpends
            ? i18n.get('费用已被移至「快速报销」，您可在「快速报销」中继续编辑')
            : i18n.get('费用已被移至「随手记」，您可在「随手记」中继续编辑')
          showMessage.info(message)
          this.onCloseModal(true)
          this.bus.emit('update:bill:list', undefined, undefined, { action: FlowAction.Nullify })
        }
      })
  }
  fnNullifyBill = () => {
    showModal.confirm({
      title: i18n.get('您是否确认要作废该单据?'),
      content: '',
      okText: i18n.get('作废'),
      cancelText: i18n.get('取消'),
      onOk: this.fnNullifyFinish
    })
  }
  fnNullifyFinish = () => {
    related.clearRelatedData()
    let { nullifyFlow, dataSource } = this.props
    const id = get(dataSource, 'id')
    const title = get(dataSource, 'form.title') || ''
    let _this = this
    if (id) {
      nullifyFlow(id).then(_ => {
        _this.bus.emit('update:bill:list', undefined, undefined, { action: FlowAction.Nullify })
        _this.__handleInsertAssist(`作废${title}单据`) // @i18n-ignore
        _this.onCloseModal(true)
      })
    } else {
      _this.bus.emit('list:select:change')
    }
  }
  handleRetract = () => {
    let { retractFlow, dataSource } = this.props
    let { id } = dataSource
    const title = get(dataSource, 'form.title') || ''
    let _this = this
    showModal.confirm({
      title: i18n.get('你确定撤回单据吗？'),
      content: i18n.get('撤回单据后，若重新提交单据，将从第一位审批人开始审批。'),
      okText: i18n.get('确认'),
      cancelText: i18n.get('取消'),
      onOk() {
        retractFlow(id).then(action => {
          if (!action.error) {
            _this.bus.emit('update:bill:list', dataSource, undefined, { action: FlowAction.Retract })
            _this.__handleInsertAssist(`撤回${title}单据`) // @i18n-ignore
            const { from, closeDrawer, callback } = _this.props
            if (from === 'from_drawer') {
              closeDrawer && closeDrawer()
              callback && callback()
            }
            // 我的单据，列表模式下
            if (from === 'from_myBill') {
              _this.onCloseModal(true)
            }
          }
        })
      }
    })
  }

  handlePrint = () => {
    const { dataSource } = this.props
    const title = get(dataSource, 'form.title') || ''
    const obj = api.invokeService('@share:get:print:param', dataSource)
    const data = [obj]
    this.__handleInsertAssist(`打印${title}单据`) // @i18n-ignore
    const { doPrint } = api.require('@audit/service-print')
    doPrint(data, false, () => this.bus.emit('list:line:click', dataSource), false, '0')
  }

  handlePrintInvoice = () => {
    const { dataSource } = this.props
    const title = get(dataSource, 'form.title') || ''
    const obj = api.invokeService('@share:get:print:param', dataSource)
    const data = [obj]
    this.__handleInsertAssist(`打印${title}单据`) // @i18n-ignore
    const { doPrint } = api.require('@audit/service-print')
    doPrint(data, false, () => this.bus.emit('list:line:click', dataSource), false, '1')
  }

  handleRemind = () => {
    let { dataSource } = this.props
    const title = get(dataSource, 'form.title') || ''
    showModal.confirm({
      className: 'confirmCopyModal-wrapper',
      title: i18n.get('发送催办消息'),
      content: i18n.get(
        `系统将发送一条消息提醒 {__k0} 审批。不建议频繁使用此功能催促审批人。若长时间没有审批，建议通过电话等其他联系方式联系审批人。`,
        { __k0: this.getApproveMember() }
      ),
      okText: i18n.get('确认'),
      cancelText: i18n.get('取消'),
      onOk: () => {
        let { lastTime } = this.state
        let newTime = new Date().valueOf()
        if (newTime - lastTime > 60000) {
          //60秒内只能执行一次催办功能
          this.fnReminde()
          this.__handleInsertAssist(`催办${title}单据`) // @i18n-ignore
          this.setState({ lastTime: newTime })
        } else {
          showMessage.warning(i18n.get('操作频繁'))
        }
      }
    })
  }

  handleComment = () => {
    const { dataSource, bus } = this.props
    const flowId = dataSource.id
    const _this = this
    const title = get(dataSource, 'form.title') || ''
    api.open('@bills:BillCommentModal', { flow: dataSource }).then(params => {
      api.invokeService('@bills:comment:flow', { id: flowId, params }).then(action => {
        if (!action.error) {
          _this.bus.emit('list:line:click', dataSource)
          _this.__handleInsertAssist(`评论${title}单据`) // @i18n-ignore
          bus.emit('bills:update:flow', dataSource)
        }
      })
    })
  }

  fnReminde = () => {
    const { dataSource } = this.props
    const flowId = get(dataSource, 'id')
    const taskId = get(dataSource, 'plan.taskId')
    api.invokeService('@bills:bill:reminde', flowId, taskId).then(this.reloadData)
  }

  handleCopy = async () => {
    // 单据使用的模板被停用时，拦截复制动作
    const specificationOriginalId = this.getCurrencySpecOriginalId()
    const specActive = await checkSpecificationActive(specificationOriginalId)
    if (!specActive) return

    const { dataSource, from } = this.props
    delete dataSource.form.systemGeneration

    // 复制单据不能带费用明细序号，序号应由后端生成
    if (dataSource?.form?.details?.length) {
      dataSource.form.details = dataSource.form.details?.map(detail => {
        delete detail.feeTypeForm.detailNo
        return detail
      })
    }
    // 复制单据不能带行程id，行程应由后端生成
    if (dataSource?.form?.travelPlanning) {
      dataSource.form.travelPlanning = dataSource.form.travelPlanning?.map(travel => {
        travel.travelId = null
        return travel
      })
    }

    if (from === 'from_drawer' || from === 'from_myBill') {
      this.handleDrawerCopy(dataSource)
    } else {
      this.bus.emit('copy:bill', dataSource)
      const title = get(dataSource, 'form.title') || ''
      this.__handleInsertAssist(`复制${title}单据`) // @i18n-ignore
    }
  }

  handleDrawerCopy = dataSource => {
    confirmCopy(dataSource).then(_ => {
      this.handleCopyBill(dataSource)
    })
  }
  handleCopyBill = async dataSource => {
    const { from, closeDrawer } = this.props
    const data = await formatCopyBillData(dataSource)
    api.open('@bills:BillEditableDrawer', {
      data,
      from,
      closeDrawer,
      callback: () => {
        this.onCloseModal(false)
      }
    })
  }

  preview = () => {
    const { dataSource, bus } = this.props
    api.invokeService('@bills:flow:preview', dataSource, bus)
  }
  fnCreateNodesInstance = nodes => {
    let random = (min, max) => Math.floor(Math.random() * (max - min)) + min
    forEach(nodes, o => {
      o.id = `FLOW:${random(0, 2147483647)}:${random(0, 2147483647)}`
    })
    return nodes
  }

  getActionMap = (scanViewData = {}) => {
    const { fn } = scanViewData
    return {
      [FlowAction.Submit]: {
        onClick: this.handleSubmit
      },
      [FlowAction.Save]: {
        onClick: this.handleSave
      },
      [FlowAction.Delete]: {
        onClick: this.handleDel
      },
      [FlowAction.Print]: {
        onClick: this.handlePrint
      },
      [FlowAction.PrintInvoice]: {
        onClick: this.handlePrintInvoice
      },
      [FlowAction.Comment]: {
        onClick: this.handleComment
      },
      [FlowAction.Copy]: {
        onClick: this.handleCopy
      },
      [FlowAction.Nullify]: {
        onClick: this.handleNullify
      },
      [FlowAction.Cancel]: {
        onClick: fn
      },
      [FlowAction.Urge]: {
        onClick: this.handleRemind
      },
      [FlowAction.Retract]: {
        onClick: this.handleRetract
      },
      [FlowAction.Share]: {
        onClick: () => {
          const { dataSource } = this.props
          shareBillAction(dataSource?.id)
        }
      }
    }
  }
  getButtonGroup(state, plan, scanViewData, forbidOwnerPrint = false) {
    const {
      dataSource,
      bus,
      from,
      canNullify,
      closeDrawer,
      dataSource: { requisitionInfo, currentSpecification, formType, form = {} }
    } = this.props
    const { saveLoading, submitLoading, disabled } = this.state
    const specification = currentSpecification || form.specificationId || requisitionInfo?.defaultSpecification || {}
    const isEBussCard = specification?.configs?.find(item => item?.isEBussCard)
    const alterFlag = form.alterFlag
    // 合同单变更标识
    const contractCode = form.contractCode
    const isAmortizeExpense = formType === 'amortizeExpense'
    const isShowDelete = !isEBussCard && !contractCode && !(alterFlag && alterFlag >= '1') && !isAmortizeExpense

    if (dataSource?.openFrom === 'flow-preview') {
      const { fn } = scanViewData
      const actions = [
        {
          label: i18n.get('预览'),
          main: true,
          onClick: () => {
            if (from === 'from_drawer') {
              window.TRACK &&
                window.TRACK('Preview_button', {
                  actionName: i18n.get('从搜索单子中点击「预览」'),
                  corpId: Fetch?.corpId
                })
              this.preview()
            } else {
              window.TRACK &&
                window.TRACK('Preview_button', {
                  actionName: i18n.get('从新建单子中点击「预览」'),
                  corpId: Fetch?.corpId
                })
              this.handleSubmit()
            }
          },
          disabled: disabled,
          loading: submitLoading
        },
        {
          label: i18n.get('取消'),
          onClick: fn
        }
      ]
      return { actions }
    }
    if (dataSource?.openFrom === 'permit-form') {
      // 商城预置单填单
      const { fn } = scanViewData
      const actions = [
        {
          label: i18n.get('保存'),
          main: true,
          onClick: () => {
            console.log('-----保存-----')
            this.handleSubmit()
          },
          disabled: disabled,
          loading: submitLoading
        },
        {
          label: i18n.get('取消'),
          onClick: () => {
            console.log('-----取消-----')
            if (this.bus?.__CURRENT_IS_CHANGED) {
              showModal.confirm({
                title: i18n.get('尚未保存，是否确认返回？'),
                onOk: () => {
                  closeDrawer && closeDrawer()
                  fn && fn()
                }
              })
            } else {
              closeDrawer && closeDrawer()
              fn && fn()
            }
          }
        }
      ]
      return { actions }
    }

    const deleteAction = canNullify?.done
      ? {
        action: FlowAction.Delete,
        label: i18n.get('删除单据'),
        onClick: this.handleDel,
        dangerous: true
      }
      : null

    const nullifyAction = canNullify?.value
      ? {
        action: FlowAction.Nullify,
        label: i18n.get('作废'),
        onClick: this.handleNullify
      }
      : null
    if (scanViewData) {
      const { fn } = scanViewData
      const actions = [
        {
          action: FlowAction.Submit,
          label: i18n.get('提交送审'),
          main: true,
          onClick: this.handleSubmit,
          disabled: disabled,
          loading: submitLoading
        },
        {
          action: FlowAction.Save,
          label: i18n.get('存为草稿'),
          onClick: this.handleSave,
          disabled: disabled,
          loading: saveLoading
        },
        {
          action: FlowAction.Cancel,
          label: i18n.get('取消'),
          onClick: fn
        }
      ]
      return { actions }
    }

    if (state === 'receivingExcep') {
      let actions = [
        {
          action: FlowAction.Urge,
          label: i18n.get('催办'),
          onClick: this.handleRemind
        },
        {
          action: FlowAction.Copy,
          label: i18n.get('复制'),
          onClick: this.handleCopy
        },
        {
          action: FlowAction.Comment,
          label: i18n.get('评论'),
          onClick: this.handleComment
        }
      ]
      if (!forbidOwnerPrint) {
        actions.push({
          action: FlowAction.Print,
          label: i18n.get('打印单据'),
          onClick: this.handlePrint
        })
        if (this.state.showPrintBtn) {
          actions.push({
            action: FlowAction.PrintInvoice,
            label: i18n.get('打印单据和发票'),
            onClick: this.handlePrintInvoice
          })
        }
      }
      return { actions }
    }

    if (state === 'paying' || state === 'approving' || state === 'sending' || state === 'receiving') {
      let actions = [
        {
          action: FlowAction.Comment,
          label: i18n.get('评论'),
          main: true,
          onClick: this.handleComment
        },
        {
          action: FlowAction.Urge,
          label: i18n.get('催办'),
          onClick: this.handleRemind
        }
      ]

      //根据审批流配置,后台下发是否显示打印按钮
      if (!forbidOwnerPrint) {
        if (this.state.showPrintBtn) {
          actions.unshift({
            action: FlowAction.PrintInvoice,
            label: i18n.get('打印单据和发票'),
            onClick: this.handlePrintInvoice
          })
        }
        actions.unshift({
          action: FlowAction.Print,
          label: i18n.get('打印单据'),
          onClick: this.handlePrint
        })
      }
      actions.push({
        action: FlowAction.Copy,
        label: i18n.get('复制'),
        onClick: this.handleCopy
      })

      if (plan) {
        let { nodes, taskId } = plan
        let node = nodes && nodes.find(node => node.id === taskId)
        let config = (node && node.config) || {}

        if (node && config.allowSubmitterRetract) {
          actions.unshift({
            action: FlowAction.Retract,
            label: i18n.get('撤回'),
            onClick: this.handleRetract
          })
        }
      }

      if (['reconciliation', 'settlement'].includes(formType)) {
        actions = actions.filter(v => v.label !== i18n.get('复制'))
      }
      return { actions }
    }

    if (state === 'paid' || state === 'archived') {
      const actions = [
        {
          action: FlowAction.Comment,
          label: i18n.get('评论'),
          main: true,
          onClick: this.handleComment
        }
      ]
      if (!forbidOwnerPrint) {
        if (this.state.showPrintBtn) {
          actions.unshift({
            action: FlowAction.PrintInvoice,
            label: i18n.get('打印单据和发票'),
            onClick: this.handlePrintInvoice
          })
        }
        actions.unshift({
          action: FlowAction.Print,
          label: i18n.get('打印单据'),
          onClick: this.handlePrint
        })
      }
      if (!['reconciliation', 'settlement'].includes(formType)) {
        actions.push({
          action: FlowAction.Copy,
          label: i18n.get('复制'),
          onClick: this.handleCopy
        })
      }

      return { actions }
    }

    if (state === 'pending') {
      return {}
    }

    if (state === 'rejected') {
      const actions = [
        {
          action: FlowAction.Submit,
          label: i18n.get('提交送审'),
          main: true,
          onClick: this.handleSubmit,
          disabled: disabled,
          loading: submitLoading
        },
        {
          action: FlowAction.Save,
          label: i18n.get('存为草稿'),
          onClick: this.handleSave,
          disabled: disabled,
          loading: saveLoading
        },
        {
          action: FlowAction.Copy,
          label: i18n.get('复制'),
          onClick: this.handleSaveAndCopy
        },
        {
          action: FlowAction.Comment,
          label: i18n.get('评论'),
          onClick: this.handleComment
        }
      ]
      if (isShowDelete && deleteAction) {
        actions.push(deleteAction)
      }
      return { actions, nullifyAction }
    }

    const actions = [
      {
        action: FlowAction.Submit,
        label: i18n.get('提交送审'),
        main: true,
        onClick: this.handleSubmit,
        disabled: disabled,
        loading: submitLoading
      },
      {
        action: FlowAction.Save,
        label: i18n.get('存为草稿'),
        onClick: this.handleSave,
        disabled: disabled,
        loading: saveLoading
      }
    ]
    if (dataSource?.id) {
      actions.push({
        action: FlowAction.Copy,
        label: i18n.get('复制'),
        onClick: this.handleSaveAndCopy
      })
    }

    if (isShowDelete && deleteAction) {
      actions.push(deleteAction)
    }

    return { actions, nullifyAction }
  }

  getApproveMember = () => {
    const { taskId, nodes } = this.props.dataSource.plan
    const currentNode = nodes.find(node => node.id === taskId)
    if (currentNode.type === 'countersign') {
      const approvingSigners = currentNode.counterSigners
        .filter(item => item.state === 'APPROVING' || item.state === null)
        .map(item => item.signerId.name)
      return i18n.get(`{__k0}等{__k1}人`, {
        __k0: approvingSigners.slice(0, 10).join(),
        __k1: approvingSigners.length
      })
    } else if (currentNode.type === 'ebot') {
      return 'Ebot'
    } else if (currentNode.type === 'invoicingApplication') {
      return i18n.get('开票申请')
    } else {
      return currentNode.approverId ? currentNode.approverId.name : i18n.get('未选择')
    }
  }

  onBack = () => {
    let { onCloseModal } = this.props
    // TODO: 检测是否能够直接返回,可返回 直接关闭,不能返回提醒
    this.bus.invoke('check:value:changed').then(
      res => {
        onCloseModal && onCloseModal()
      },
      error => {
        if (error === 'cancel') return
        onCloseModal && onCloseModal()
      }
    )
  }
  renderDeleteOrNullify = (isEBussCard, nullifyAction) => {
    if (!isEBussCard && nullifyAction) {
      return <NullifyButton button={nullifyAction} />
    }
    // if (!isEBussCard && !contractCode && !(alterFlag && alterFlag >= '1') && !isAmortizeExpense) {
    //   return <DeleteButton button={deleteAction} />
    // }
    return null
  }

  render() {
    let {
      dataSource,
      scanViewData,
      creatStyle,
      onCloseModal,
      onSetting,
      autoExpenseWithBillStriction,
      hiddenCancel = false,
      showEmptyPage,
      isLoading,
      scene,
      privilegeId
    } = this.props
    if (!showEmptyPage && isLoading) {
      return <SkeletonButton length={4} />
    }
    if (!dataSource) return null
    if (creatStyle === 'new_modal_style') {
      return (
        <Header
          saveTitle={'最近保存：刚刚'}
          onBack={this.onBack}
          onSubmit={this.handleSubmit}
          onSave={this.handleSave}
          onDelete={this.handleDel}
          onSetting={onSetting}
        />
      )
    }
    let { state, plan, form = {}, formType, currentSpecification, requisitionInfo } = dataSource
    let { actions, nullifyAction } = this.getButtonGroup(state, plan, scanViewData, form.forbidOwnerPrint)
    // 表格预览时，在弹窗内展示打印预览结果
    const isReadOnlyState = state !== 'new' && state !== 'modify' && state !== 'rejected' && state !== 'draft'

    if (isReadOnlyState) {
      // 根据条件判断是否添加分享按钮
      fnPushDingTalkShareBtn(actions, get(dataSource, 'id'))
    }

    // 变更标识
    // "0" 审批通过
    // >="1" 变更中 不可删除
    const alterFlag = form.alterFlag
    // 合同单变更标识
    const contractCode = form.contractCode
    const specification = currentSpecification || form.specificationId || requisitionInfo?.defaultSpecification || {}
    const isEBussCard = specification?.configs?.find(item => item?.isEBussCard)
    if (!actions || !actions.length) return <></>
    actions = isPrintShow({ selectedRowsData: dataSource })
      ? actions
      : actions.filter(
        line =>
          line.label !== i18n.get('打印单据') &&
          line.label !== i18n.get('打印单据和发票') &&
          line.label !== i18n.get('打印提醒')
      )
    if (hiddenCancel) {
      actions = actions.filter(v => v.label !== i18n.get('取消'))
    }
    const { isQuickExpends } = this.state
    const isAmortizeExpense = formType === 'amortizeExpense'

    if (
      isEBussCard ||
      isQuickExpends ||
      contractCode ||
      formType === 'corpPayment' ||
      isAmortizeExpense ||
      (formType === 'expense' && autoExpenseWithBillStriction)
    ) {
      actions = actions.filter(v => v.label !== i18n.get('复制'))
    }
    const needConfigButton = dataSource?.openFrom !== 'flow-preview' && dataSource?.openFrom !== 'permit-form'

    if (!isEBussCard && nullifyAction) {
      actions.push(nullifyAction)
    }

    return (
      <BillInfoButtons
        flowActionButtons={actions}
        flowId={dataSource.id}
        scene={scene}
        privilegeId={privilegeId}
        needConfigButton={needConfigButton}
        flowActionMap={this.getActionMap(scanViewData)}
      >
        <div className={styles['right-part-footer']} ref={this.containerRef}>
          <FlexableButtonGroup moreText={i18n.get('更多')} buttons={actions} useFakeDisabled />
        </div>
      </BillInfoButtons>
    )
  }
}

/**
 * @return {null}
 */
function DeleteButton(props) {
  const { button } = props
  if (!button) {
    return null
  }
  const { label, onClick } = button
  return (
    <div className="delete_button_wrapper" onClick={onClick}>
      <EKBIcon name="#EDico-delete" className="icon-delete" />
      {label}
    </div>
  )
}
/**
 * @return {null}
 */
function NullifyButton(props) {
  const { button } = props
  if (!button) {
    return null
  }
  const { label, onClick } = button
  return (
    <span className="nullify_button" onClick={onClick}>
      {label}
    </span>
  )
}
