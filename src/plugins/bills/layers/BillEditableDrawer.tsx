/**************************************
 * Created By LinK On 2019/2/14 16:35.
 **************************************/
import React, { PureComponent } from 'react'
import { EnhanceDrawer } from '@ekuaibao/enhance-layer-manager'
import MessageCenter from '@ekuaibao/messagecenter'
import BillInfoEditableContainer from '../parts/right-part/billInfo/BillInfoEditableContainer'
import RightPartFooter from '../parts/right-part/billInfo/RightPartFooter'
import { setValidateError } from '../bills.action'
import { EnhanceConnect } from '@ekuaibao/store'
import { enableCustomExtendButton } from '../../../lib/featbit'
import { BillFooter } from '../bill-details-new/BillFooter'

@EnhanceConnect(null, { setValidateError })
@EnhanceDrawer()
export default class BillEditableDrawer extends PureComponent<any, any> {
  constructor(props) {
    super(props)
    this.bus = props.bus || new MessageCenter()
  }

  handleCancel = res => {
    const { callback, layer } = this.props
    console.log('---handleCancel-----', res)
    res && callback && callback(res)
    layer.emitCancel()
  }

  renderFooterV1 = () => {
    const { data, from, closeDrawer, setValidateError, } = this.props
    return <RightPartFooter
      from={from}
      closeDrawer={closeDrawer}
      bus={this.bus}
      dataSource={data}
      scanViewData={{ fn: this.handleCancel }}
      scene={'OWNER'}
      setValidateError={setValidateError}
    />
  }

  renderNewFooter = () => {
    const { data, from, layer, } = this.props
    return <BillFooter
      // @ts-ignore
      bus={this.bus}
      flow={data}
      layer={layer}
      forceUpdateFlow={() => {}}
      from={from}
      closeAfterAction
    />
  }

  isFromHoseTMC = () => {
    return this.props.data?.openFrom === 'permit-form'
  }


  getFooter = () => {
    if (enableCustomExtendButton() && !this.isFromHoseTMC()) {
      return this.renderNewFooter()
    } else {
      return this.renderFooterV1()
    }
  }

  render() {
    const { data, riskData, from, closeDrawer, setValidateError, showUpDown, layer } = this.props
    return (
      <div className="dis-f fd-c flex-1" style={{ width: '100%', height: '100%' }}>
        <BillInfoEditableContainer
          bus={this.bus}
          dataSource={data}
          riskData={riskData}
          closeDrawer={closeDrawer}
          hiddenCancel={true}
          scanViewData={{ fn: this.handleCancel }}
          showUpDown={showUpDown}
          layer={layer}
        />
        {this.getFooter()}
      </div>
    )
  }
}
