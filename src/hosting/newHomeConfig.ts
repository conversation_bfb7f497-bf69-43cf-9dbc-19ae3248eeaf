import { initialize } from './i18n-initialize'
import { Fetch } from '@ekuaibao/fetch'
import { initFeatBit, getBoolVariation } from '../lib/featbit'


// 生成唯一的会话ID
export const generateAIChatSessionId = () => {
  return `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}


export const getPreviewUrl = async () => {
  const defaultPreviewURL = 'https://doc.ekuaibao.com'
  window.CURRENT_AICHAT_SESSIONID = generateAIChatSessionId()
  try {
    const { value } = await Fetch.GET(`${location.origin}/api/previewUrl`, undefined, undefined, { hiddenLoading: true })
    window.PREVIEW_DOMAIN = value || defaultPreviewURL
  } catch (error) {
    window.PREVIEW_DOMAIN = defaultPreviewURL
  }
}

export async function newHomeConfig({needInitFeatBit = true, needInitI18n = true, needInitPreviewUrl = true} = {}) {
  window.isNewHome = true
  if (needInitPreviewUrl) {
    await getPreviewUrl()
  }

  if (needInitFeatBit) {
    try {
      await initFeatBit()
    } catch {
      // ignore
    }
  }
  if (IS_HSFK || !needInitI18n) return Promise.resolve()
  // 获取到language，重新初始化语言包
  return initialize()
}
