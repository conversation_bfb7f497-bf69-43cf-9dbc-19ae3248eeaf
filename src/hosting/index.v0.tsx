/**************************************************
 * Created by nany<PERSON>ingfeng on 2018/9/5 10:46.
 * UPDATE : wangguanjia DATE: 21/09/2022
 **************************************************/
require('./env')

import React from 'react'
import { app, renderAsApp } from '@ekuaibao/whispered'
import Patches from './patches'
import { run } from '@ekuaibao/mfe-dock'
import './config'
import { postStoreInitialized } from './postStoreInitialized'
require('./_plugins')

const loadMfePlugin = async () => {
  // 预先异步集成，使用异步Script提前加载数据
  // 详见：[微前端体系方案“Whispered 架构集成”部分](https://hose2019.feishu.cn/wiki/wikcnUlxReXKtAZHGm86PSGllYb#h2mvlw)
  try {
    await run({
      // when docker initialize or changed
      // if host don't support mfe ecosystem, ekbLoader should stop
      // get mfe protocol and set extended resource
      onChanged: function() {
        window.ekbLoader?.setExtendRes(window.MFE_REGISTRY_LIST?.length || 0)
      },
      // stage 2: load mfe protocol's resource
      onMfeChanged: function(event) {
        // mfe load Script callback and drive loading's second stage animation
        window.ekbLoader?.resLoaded(true, event)
      }
    })
  } catch (ex) {
    console.error('index.v0.tsx:callback MfeDock.run()', ex)
  }
}

let mfeLoadPromise = null
export const preloadMfePlugin = async () => {
  mfeLoadPromise = loadMfePlugin()
  return mfeLoadPromise
}

export async function callback() {
  await (mfeLoadPromise || loadMfePlugin())
  mfeLoadPromise = null

  const dom = await app.callback(children => <Patches>{children}</Patches>)
  await renderAsApp(dom)

  // 一个通用的初始化方法，调用时机需要在api(app)初始化之后
  postStoreInitialized()
}

export { app }

export default app
