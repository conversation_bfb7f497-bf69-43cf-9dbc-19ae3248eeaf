import '../polyfill'

import { reportHomePageLoadedDuration, reportHomePagePaintDuration } from '../../logger/homePageLoad'
import { appDataInitialize } from './bootstrap'
import { fetchHandleError } from '../FetchConfig'
import { handleError } from './util'

window.addEventListener('load', () => {
  reportHomePageLoadedDuration()
  reportHomePagePaintDuration()
})

fetchHandleError(handleError)
appDataInitialize()
window.addEventListener('DOMContentLoaded', async () => {
  await require('./bootstrap').bootstrap()
})
